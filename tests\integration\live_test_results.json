{"api_connections": {"gemini": {"passed": true, "message": "Google Gemini API connection successful"}, "elevenlabs": {"passed": true, "message": "ElevenLabs API connection successful. User: Unknown"}, "twitch": {"passed": true, "message": "Twitch API credentials are valid"}, "ollama": {"passed": false, "message": "Model 'llama2' not found. Available models: mistral-small3.1:latest, llama3.2:latest, gemma3:latest, deepseek-r1:1.5b, nomic-embed-text:latest, mxbai-embed-large:latest"}}, "ai_engine": {"initialization": true, "response_generation": false, "personality_system": true, "error_handling": true}, "audio_processor": {"initialization": true, "tts_generation": true, "voice_settings": true, "queue_processing": true}, "event_system": {"event_emission": true, "event_handling": true, "ai_response_flow": false, "audio_generation_flow": false}, "end_to_end": {"complete_flow": false, "response_time": 1.007545, "data_integrity": false}}